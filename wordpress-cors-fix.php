<?php
/**
 * WordPress CORS Fix for WooNuxt
 * Add this code to your WordPress theme's functions.php file
 * or create a custom plugin with this code
 */

// Add CORS headers for GraphQL requests
add_action('init', function() {
    // Only add CORS headers for GraphQL requests
    if (strpos($_SERVER['REQUEST_URI'], '/graphql') !== false) {
        
        // Get the origin of the request
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        
        // List of allowed origins (add your frontend URLs here)
        $allowed_origins = [
            'http://*************:3000',
            'http://localhost:3000',
            'https://*************:3000',
            'https://localhost:3000'
        ];
        
        // Check if the origin is allowed
        if (in_array($origin, $allowed_origins)) {
            header("Access-Control-Allow-Origin: $origin");
        }
        
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Origin, Accept');
        
        // Handle preflight OPTIONS requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
});

// Alternative: Use WordPress hooks for WPGraphQL specifically
add_filter('graphql_cors_allowed_domains', function($allowed_domains) {
    $allowed_domains[] = 'http://*************:3000';
    $allowed_domains[] = 'http://localhost:3000';
    $allowed_domains[] = 'https://*************:3000';
    $allowed_domains[] = 'https://localhost:3000';
    return $allowed_domains;
});

// Enable CORS for WPGraphQL
add_filter('graphql_response_headers_to_send', function($headers) {
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    
    $allowed_origins = [
        'http://*************:3000',
        'http://localhost:3000',
        'https://*************:3000',
        'https://localhost:3000'
    ];
    
    if (in_array($origin, $allowed_origins)) {
        $headers['Access-Control-Allow-Origin'] = $origin;
        $headers['Access-Control-Allow-Credentials'] = 'true';
        $headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE';
        $headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, Origin, Accept';
    }
    
    return $headers;
});
?>
