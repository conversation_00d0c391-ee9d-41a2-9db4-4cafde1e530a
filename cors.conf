# Apache CORS Configuration for WordPress GraphQL
# This file should be placed in the Apache configuration directory

<IfModule mod_headers.c>
    # Handle CORS for GraphQL endpoint
    <LocationMatch "/graphql">
        # Set CORS headers for specific origins
        SetEnvIf Origin "^http://192\.168\.1\.121:3000$" CORS_ORIGIN=$0
        SetEnvIf Origin "^http://localhost:3000$" CORS_ORIGIN=$0
        SetEnvIf Origin "^https://192\.168\.1\.121:3000$" CORS_ORIGIN=$0
        SetEnvIf Origin "^https://localhost:3000$" CORS_ORIGIN=$0
        
        # Send CORS headers if origin matches
        Header always set Access-Control-Allow-Origin "%{CORS_ORIGIN}e" env=CORS_ORIGIN
        Header always set Access-Control-Allow-Credentials "true" env=CORS_ORIGIN
        Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE" env=CORS_ORIGIN
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Origin, Accept" env=CORS_ORIGIN
        
        # Handle preflight OPTIONS requests
        RewriteEngine On
        RewriteCond %{REQUEST_METHOD} OPTIONS
        RewriteRule ^(.*)$ $1 [R=200,L]
    </LocationMatch>
</IfModule>
