{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,UAAU,CAAC,KAAa;IACtC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM;YACJ,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;KACzE;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["export function spongeCase(input: string): string {\n  let result = \"\";\n  for (let i = 0; i < input.length; i++) {\n    result +=\n      Math.random() > 0.5 ? input[i].toUpperCase() : input[i].toLowerCase();\n  }\n  return result;\n}\n"]}