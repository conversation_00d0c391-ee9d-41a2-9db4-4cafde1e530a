{"name": "magic-string-ast", "version": "0.7.1", "description": "magic-string with Babel AST shortcut.", "type": "module", "license": "MIT", "homepage": "https://github.com/sxzz/magic-string-ast#readme", "bugs": {"url": "https://github.com/sxzz/magic-string-ast/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sxzz/magic-string-ast.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}, "./*": "./*"}, "publishConfig": {"access": "public"}, "dependencies": {"magic-string": "^0.30.17"}, "devDependencies": {"@babel/types": "^7.26.7", "@sxzz/eslint-config": "^5.0.0-beta.2", "@sxzz/prettier-config": "^2.1.1", "@types/node": "^22.12.0", "bumpp": "^10.0.1", "eslint": "^9.19.0", "fast-glob": "^3.3.3", "magic-string-stack": "^1.0.0", "prettier": "^3.4.2", "tsup": "^8.3.6", "tsx": "^4.19.2", "typescript": "^5.7.3", "vitest": "^3.0.4"}, "engines": {"node": ">=16.14.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint .", "lint:fix": "pnpm run lint --fix", "build": "tsup", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish"}}