{"name": "globby", "version": "14.1.0", "description": "User-friendly glob matching", "license": "MIT", "repository": "sindresorhus/globby", "funding": "https://github.com/sponsors/sindresorhus", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"bench": "npm update @globby/main-branch glob-stream fast-glob && node bench.js", "test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "ignore.js", "utilities.js"], "keywords": ["all", "array", "directories", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise", "gitignore", "git"], "dependencies": {"@sindresorhus/merge-streams": "^2.1.0", "fast-glob": "^3.3.3", "ignore": "^7.0.3", "path-type": "^6.0.0", "slash": "^5.1.0", "unicorn-magic": "^0.3.0"}, "devDependencies": {"@globby/main-branch": "sindresorhus/globby#main", "@types/node": "^22.13.1", "ava": "^5.3.1", "benchmark": "2.1.4", "glob-stream": "^8.0.0", "tempy": "^3.1.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "xo": {"ignores": ["fixtures"]}, "ava": {"files": ["!tests/utilities.js"], "workerThreads": false}}