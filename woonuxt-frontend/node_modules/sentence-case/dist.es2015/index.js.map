{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAW,MAAM,SAAS,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAIlD,MAAM,UAAU,qBAAqB,CAAC,KAAa,EAAE,KAAa;IAChE,IAAM,MAAM,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACnC,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC/D,OAAO,MAAM,CAAC,KAAK,aACjB,SAAS,EAAE,GAAG,EACd,SAAS,EAAE,qBAAqB,IAC7B,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["import { noCase, Options } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\n\nexport { Options };\n\nexport function sentenceCaseTransform(input: string, index: number) {\n  const result = input.toLowerCase();\n  if (index === 0) return upperCaseFirst(result);\n  return result;\n}\n\nexport function sentenceCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \" \",\n    transform: sentenceCaseTransform,\n    ...options,\n  });\n}\n"]}