<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->








[![git-url-parse](http://i.imgur.com/HlfMsVf.png)](#)











# git-url-parse

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Travis](https://img.shields.io/travis/IonicaBizau/git-url-parse.svg)](https://travis-ci.org/IonicaBizau/git-url-parse/) [![Version](https://img.shields.io/npm/v/git-url-parse.svg)](https://www.npmjs.com/package/git-url-parse) [![Downloads](https://img.shields.io/npm/dt/git-url-parse.svg)](https://www.npmjs.com/package/git-url-parse) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/@johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A high level git url parser for common git providers.

















## :cloud: Installation

```sh
# Using npm
npm install --save git-url-parse

# Using yarn
yarn add git-url-parse
```













## :clipboard: Example



```js
// Dependencies
const GitUrlParse = require("git-url-parse");

console.log(GitUrlParse("**************:IonicaBizau/node-git-url-parse.git"));
// => {
//     protocols: []
//   , port: null
//   , resource: "github.com"
//   , user: "git"
//   , pathname: "/IonicaBizau/node-git-url-parse.git"
//   , hash: ""
//   , search: ""
//   , href: "**************:IonicaBizau/node-git-url-parse.git"
//   , token: ""
//   , protocol: "ssh"
//   , toString: [Function]
//   , source: "github.com"
//   , name: "node-git-url-parse"
//   , owner: "IonicaBizau"
// }

console.log(GitUrlParse("https://github.com/IonicaBizau/node-git-url-parse.git"));
// => {
//     protocols: ["https"]
//   , port: null
//   , resource: "github.com"
//   , user: ""
//   , pathname: "/IonicaBizau/node-git-url-parse.git"
//   , hash: ""
//   , search: ""
//   , href: "https://github.com/IonicaBizau/node-git-url-parse.git"
//   , token: ""
//   , protocol: "https"
//   , toString: [Function]
//   , source: "github.com"
//   , name: "node-git-url-parse"
//   , owner: "IonicaBizau"
// }

console.log(GitUrlParse("https://github.com/IonicaBizau/git-url-parse/blob/master/test/index.js"));
// { protocols: [ 'https' ],
//   protocol: 'https',
//   port: null,
//   resource: 'github.com',
//   user: '',
//   pathname: '/IonicaBizau/git-url-parse/blob/master/test/index.js',
//   hash: '',
//   search: '',
//   href: 'https://github.com/IonicaBizau/git-url-parse/blob/master/test/index.js',
//   token: '',
//   toString: [Function],
//   source: 'github.com',
//   name: 'git-url-parse',
//   owner: 'IonicaBizau',
//   organization: '',
//   ref: 'master',
//   filepathtype: 'blob',
//   filepath: 'test/index.js',
//   full_name: 'IonicaBizau/git-url-parse' }

console.log(GitUrlParse("https://github.com/IonicaBizau/node-git-url-parse.git").toString("ssh"));
// => "**************:IonicaBizau/node-git-url-parse.git"

console.log(GitUrlParse("**************:IonicaBizau/node-git-url-parse.git").toString("https"));
// => "https://github.com/IonicaBizau/node-git-url-parse.git"
```











## :memo: Documentation


### `gitUrlParse(url, refs)`
Parses a Git url.

#### Params

- **String** `url`: The Git url to parse.
- **Array** `refs`: An array of strings representing the refs. This is  helpful in the context of the URLs that contain branches with slashes.
 If user wants to identify the branch, he should pass all branch names
 of the project as part of refs parameter

#### Return
- **GitUrl** The `GitUrl` object containing:
 - `protocols` (Array): An array with the url protocols (usually it has one element).
 - `port` (String): The domain port.
 - `resource` (String): The url domain (including subdomains).
 - `user` (String): The authentication user (usually for ssh urls).
 - `pathname` (String): The url pathname.
 - `hash` (String): The url hash.
 - `search` (String): The url querystring value.
 - `href` (String): The input url.
 - `protocol` (String): The git url protocol.
 - `token` (String): The oauth token (could appear in the https urls).
 - `source` (String): The Git provider (e.g. `"github.com"`).
 - `owner` (String): The repository owner.
 - `name` (String): The repository name.
 - `ref` (String): The repository ref (e.g., "master" or "dev").
 - `filepath` (String): A filepath relative to the repository root.
 - `filepathtype` (String): The type of filepath in the url ("blob" or "tree").
 - `full_name` (String): The owner and name values in the `owner/name` format.
 - `toString` (Function): A function to stringify the parsed url into another url type.
 - `organization` (String): The organization the owner belongs to. This is CloudForge specific.
 - `git_suffix` (Boolean): Whether to add the `.git` suffix or not.

### `stringify(obj, type)`
Stringifies a `GitUrl` object.

#### Params

- **GitUrl** `obj`: The parsed Git url object.
- **String** `type`: The type of the stringified url (default `obj.protocol`).

#### Return
- **String** The stringified url.









## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:
















## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `@0x-lerna-fork/github-client`
 - `@1coin178/github-compare`
 - `@_nomtek/react-native-shimmer-animation`
 - `@adityasinghal26/backstage-plugin-daytona`
 - `@ahmed_shaban123/react-native-currencyinput`
 - `@akemona-org/strapi-generate-new`
 - `@amiruldev/wajs`
 - `@anakz/backstage-plugin-library-check-backend`
 - `@antv/gatsby-theme-antv`
 - `@appirio/appirio`
 - `@appworks/project-utils`
 - `@arcanis/sherlock`
 - `@ashcorrguardian/cdk`
 - `@atomist/automation-client`
 - `@atomist/automation-client-ext-raven`
 - `@atomist/cli`
 - `@atomist/sdm-pack-analysis`
 - `@atomist/skill`
 - `@atomist/uhura`
 - `@axetroy/gpm`
 - `@backstage-community/plugin-github-actions`
 - `@backstage/backend-common`
 - `@backstage/backend-defaults`
 - `@backstage/cli`
 - `@backstage/integration`
 - `@backstage/plugin-catalog-backend`
 - `@backstage/plugin-catalog-backend-module-github`
 - `@backstage/plugin-catalog-import`
 - `@backstage/plugin-github-actions`
 - `@backstage/plugin-scaffolder`
 - `@backstage/plugin-scaffolder-backend-module-confluence-to-markdown`
 - `@backstage/plugin-techdocs`
 - `@backstage/plugin-techdocs-module-addons-contrib`
 - `@backstage/plugin-techdocs-node`
 - `@belt/repo`
 - `@blackglory/git-list`
 - `@brisk-docs/gatsby-generator`
 - `@brisk-docs/website`
 - `@buschtoens/documentation`
 - `@cilyn/bitbucket`
 - `@ckatzorke/renovate`
 - `@cliz/gpm`
 - `@codemod.com/workflow`
 - `@corelmax/react-native-my2c2p-sdk`
 - `@cs6/react-native-test-native-view-library`
 - `@csmith/release-it`
 - `@ctdesarrollo/pack-up`
 - `@dandean/storybook-deployer`
 - `@daytona-io/backstage-plugin-daytona`
 - `@deskbtm/workspace-tools`
 - `@dfatwork-pkgs/backstage-cli`
 - `@dougkulak/semantic-release-gh-pages-plugin`
 - `@dtwo/telemetry`
 - `@eat-fish/changelog`
 - `@emedvedev/renovate`
 - `@enkeledi/react-native-week-month-date-picker`
 - `@env0/backstage-plugin-env0`
 - `@era-ci/utils`
 - `@erquhart/lerna-github-client`
 - `@esops/publish-github-pages`
 - `@eteg/nextra-theme-docs`
 - `@facadecompany/ignition-ui`
 - `@feizheng/next-git-url`
 - `@felipesimmi/react-native-datalogic-module`
 - `@flxbl-io/sfp`
 - `@focusworkstemp/project-utils`
 - `@form8ion/lift`
 - `@gambitnash/cli-plugin-scaffold`
 - `@gasket/plugin-metrics`
 - `@geut/chan-parser`
 - `@geut/git-compare-template`
 - `@geut/git-url-parse`
 - `@git-stack/hemera-plugin`
 - `@git-stack/server-core`
 - `@guardian/cdk`
 - `@hawkingnetwork/react-native-tab-view`
 - `@hbglobal/react-native-actions-shortcuts`
 - `@hemith/react-native-tnk`
 - `@holipoly/cli`
 - `@hugomrdias/documentation`
 - `@hygiene/plugin-github-url`
 - `@iceworks/project-utils`
 - `@infinitecsolutions/storybook-deployer`
 - `@janus-idp/backstage-plugin-topology`
 - `@jaredpalmer/workspace-tools`
 - `@jswork/next-git-url`
 - `@jswork/topics2keywords`
 - `@kadira/storybook-deployer`
 - `@kevinbds/techdocs-common`
 - `@kgit/readbility`
 - `@koumoul/gh-pages-multi`
 - `@labiebhn_/react-native-multiplier`
 - `@lehuyaa/my-assets`
 - `@lerna-lite/version`
 - `@lerna-test-v1/markdown`
 - `@lerna/create`
 - `@lerna/github-client`
 - `@lerna/legacy-package-management`
 - `@log4brains/core`
 - `@madm4ttus3r/l4bcore`
 - `@merna/github-client`
 - `@micro-app/shared-utils`
 - `@microservices/cli`
 - `@mmomtchev/documentation`
 - `@mongchhi/plugin-blocks`
 - `@monokle/synchronizer`
 - `@narfeta/catalog-backend`
 - `@navabi/react-native-ssl-pinning`
 - `@nuxt/content`
 - `@nuxt/telemetry`
 - `@nuxt/ui-pro`
 - `@nuxthq/studio`
 - `@oumi/block-sdk`
 - `@oumi/cli-ui`
 - `@pageshare/cli`
 - `@parallelnft/web3modal`
 - `@pipelinedoc/cli`
 - `@plone/scripts`
 - `@pmworks/project-utils`
 - `@polearn/core`
 - `@positionex/position-sdk`
 - `@praella/localisationist`
 - `@pubbo/github-client`
 - `@pubgcorp/semantic-release-gitlabmonorepo`
 - `@pvdlg/semantic-release`
 - `@pvm/core`
 - `@qiwi/semantic-release-gh-pages-plugin`
 - `@qoopido/lerna.version`
 - `@radjs/block-sdk`
 - `@rdfrontier/plugin-mobile`
 - `@react-18-pdf/root`
 - `@rianfowler/backstage-backend-common`
 - `@rnx-kit/build`
 - `@roadiehq/backstage-plugin-github-insights`
 - `@roadiehq/backstage-plugin-github-pull-requests`
 - `@roadiehq/backstage-plugin-security-insights`
 - `@rocali/apollo`
 - `@s-ui/mono`
 - `@s-ui/ssr`
 - `@s-ui/studio`
 - `@safaricom/strapi`
 - `@safely-project/safely-ts`
 - `@salla.sa/cli`
 - `@sanity/pkg-utils`
 - `@sanv/apify-shared`
 - `@scafflater/scafflater`
 - `@secustor/backstage-plugin-renovate-common`
 - `@servable/manifest`
 - `@servable/tools`
 - `@shopgate/pwa-releaser`
 - `@sobird/actions`
 - `@speakeasy-sdks/nextra-theme-docs`
 - `@spotify/backstage-plugin-soundcheck-backend-module-gitlab`
 - `@spryker-lerna/github-client`
 - `@ssaitho/nextra-theme-docs`
 - `@stackbit/cms-git`
 - `@stackbit/dev-common`
 - `@status-im/react-native-transparent-video`
 - `@stepsec/release`
 - `@storybook/storybook-deployer`
 - `@strapi/pack-up`
 - `@strapi/sdk-plugin`
 - `@strapi/strapi`
 - `@syedt/hellosdk`
 - `@tahul/ui-fix`
 - `@taingo97/react-native-sunmi-printer`
 - `@taingo97/react-native-telpo-printer`
 - `@tasenor/common-node`
 - `@terasky/backstage-plugin-crossplane-claim-updater`
 - `@theowenyoung/gatsby-source-git`
 - `@tmplr/node`
 - `@toanz/strapi-generate-new`
 - `@umijs/block-sdk`
 - `@unibtc/release-it`
 - `@useoptic/optic`
 - `@vamsikc/plugin-catalog`
 - `@veecode-platform/plugin-github-actions`
 - `@visulima/nextra-theme-docs`
 - `@voodeng/uppacks`
 - `@vrabbi/plugin-scaffolder`
 - `@vrabbi/plugin-scaffolder-backend`
 - `@wetrial/block-sdk`
 - `@whhjdi/nextra-theme-docs`
 - `@wolfy1339/gatsby-source-git`
 - `@x-team/strapi-generate-new`
 - `@xdn/cli`
 - `@xyz/create-package`
 - `@yarnpkg/plugin-git`
 - `@zeplin/cli`
 - `anakketiga`
 - `anaklanangtea`
 - `anakwadontea`
 - `apify-shared`
 - `apollo`
 - `auto-changelog-vsts`
 - `autorelease-setup`
 - `belt-repo`
 - `biometric-st`
 - `bitbucket-pullr`
 - `changelog.md`
 - `clipped`
 - `codesandbox-theme-docs`
 - `common-boilerplate`
 - `copy-github-directory`
 - `cover-builder`
 - `create-apex-js-app`
 - `create-release-it`
 - `cz-conventional-changelog-befe`
 - `datoit-generate-new`
 - `debone`
 - `development-efficiency`
 - `docula-ui`
 - `docula-ui-express`
 - `documentation`
 - `documentation-custom-markdown`
 - `domaincomponent`
 - `dx-scanner`
 - `ewizard-cli`
 - `fluent.adflow.reactnativesdk-alpha`
 - `flutter-boot`
 - `fmsl`
 - `fotingo`
 - `framework_test_library_sixdee`
 - `fster`
 - `ftl-renovate`
 - `gatsby-source-git`
 - `gatsby-source-git-remotes`
 - `gatsby-source-npmjs`
 - `gatsby-theme-zh`
 - `gaurav-react-native-loop`
 - `gcpayz-block-sdk`
 - `generate-github-directory`
 - `generator-openapi-repo`
 - `gerimismalamsenin`
 - `ghseek`
 - `git-issues`
 - `git-origin-check`
 - `git-service-node`
 - `git-source`
 - `git-url-promise`
 - `git-yoink`
 - `gitbook-start-iaas-bbdd-alex-moi`
 - `gitbook-start-iaas-ull-es-merquililycony`
 - `gitbook-start-plugin-iaas-ull-es-noejaco2017`
 - `gitc`
 - `github-action-readme`
 - `github-publish-npm`
 - `gitlab-ci-variables-cli`
 - `gtni`
 - `harry-reporter`
 - `hzero-block-sdk`
 - `jrennsoh88-react-native-scroll-indicator`
 - `jsnix`
 - `kef-core`
 - `khaled-salem-custom-components`
 - `konfig-release-it`
 - `konitor`
 - `kuqoi-git`
 - `l-other-data`
 - `laborious`
 - `lambda-service`
 - `lcov-server`
 - `lerna`
 - `library-nuxt-ui`
 - `license-analysis`
 - `lime-cli`
 - `manage-dependency`
 - `mangudinlagirajin`
 - `mdnext-loader`
 - `mf-cta-testing`
 - `miguelcostero-ng2-toasty`
 - `native-apple-login`
 - `native-kakao-login`
 - `nextra-theme-docs-mdxcomponents`
 - `ng-apollo`
 - `npm_one_1_2_3`
 - `npm_qwerty`
 - `octokit-downloader`
 - `octopulse`
 - `omg`
 - `open-pull-request`
 - `package-health-checker`
 - `package-json-from-git`
 - `patchanon-cli`
 - `patepangdeui`
 - `pbc`
 - `pileuleuyantea`
 - `pr-changelog-gen`
 - `prep-barv11`
 - `project-wajs-dv`
 - `publish-version`
 - `pupils-generate-new`
 - `pyreswap-sdk`
 - `qts-fission`
 - `quick-release`
 - `quinntainer`
 - `rdmobile`
 - `reablocks-docs-theme`
 - `react-native-android-native-view`
 - `react-native-android-video-player-view`
 - `react-native-animate-text`
 - `react-native-arunjeyam1987`
 - `react-native-arunmeena1987`
 - `react-native-awesome-module-two`
 - `react-native-azure-communication-services`
 - `react-native-basic-app`
 - `react-native-basic-screen`
 - `react-native-biometric-authenticate`
 - `react-native-bleccs-components`
 - `react-native-bubble-chart`
 - `react-native-build-vesion-getter`
 - `react-native-check-component`
 - `react-native-components-design`
 - `react-native-contact-list`
 - `react-native-cplus`
 - `react-native-create-video-thumbnail`
 - `react-native-custom-poccomponent`
 - `react-native-datacapture-core`
 - `react-native-dhp-printer`
 - `react-native-fedlight-dsm`
 - `react-native-ghn-ekyc`
 - `react-native-ideo-rn-notifications`
 - `react-native-innity-remaster`
 - `react-native-input-library`
 - `react-native-is7`
 - `react-native-kakao-maps`
 - `react-native-klarify-ios`
 - `react-native-klc`
 - `react-native-library-testing-422522`
 - `react-native-login-demo-test`
 - `react-native-manh-test`
 - `react-native-module-for-testing`
 - `react-native-multiplier-component`
 - `react-native-multiplier-demo`
 - `react-native-multiplier2`
 - `react-native-multiselector`
 - `react-native-my-first-try-arun-ramya`
 - `react-native-nice-learning`
 - `react-native-paynow-generator`
 - `react-native-payu-payment-testing`
 - `react-native-progress-arrow`
 - `react-native-rabbitmq-all`
 - `react-native-reanimated-sortable-list`
 - `react-native-rom-components`
 - `react-native-s-airlines`
 - `react-native-sandycomponent`
 - `react-native-savczuk-feature-library`
 - `react-native-shared-gesture`
 - `react-native-test-comlibrary`
 - `react-native-ticker-tape`
 - `react-native-uvc-camera-android`
 - `react-native-withframework-check`
 - `react-native-ytximkit`
 - `redhatinsights-plugin-scaffolder-backend`
 - `refinejs-repo`
 - `release-it`
 - `release-it-http`
 - `remax-stats`
 - `renovate`
 - `renovate-csm`
 - `repom`
 - `repomix`
 - `rn-adyen-dropin`
 - `rn-check-btn`
 - `rn_unique_device_id`
 - `scaffolder-core`
 - `sedanbosok`
 - `semantic-release-github-milestones`
 - `semantic-release-gitmoji`
 - `semantic-release-squash3`
 - `sherry-utils`
 - `simple-github-release`
 - `snipx`
 - `spk`
 - `strapi-generate-new`
 - `stylelint-formatter-utils`
 - `sync-repos`
 - `tanyao`
 - `tegit`
 - `tehmusimhujan`
 - `template-typescript-node-package`
 - `test-library-123`
 - `testarmada-midway`
 - `tldw`
 - `todo2issue`
 - `tooling-personal`
 - `toxics-rpc`
 - `umi-build-dev`
 - `vision-camera-plugin-face-detector`
 - `vuepress-plugin-remote-url`
 - `wiby`
 - `winx-form-winx`
 - `workspace-tools`
 - `wsj.gatsby-source-git`
 - `zephyr-agent`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
