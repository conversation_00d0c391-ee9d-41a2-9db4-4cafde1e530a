import { snakeCase } from ".";
var TEST_CASES = [
    ["", ""],
    ["_id", "id"],
    ["test", "test"],
    ["test string", "test_string"],
    ["Test String", "test_string"],
    ["TestV2", "test_v2"],
    ["version 1.2.10", "version_1_2_10"],
    ["version 1.21.0", "version_1_21_0"],
];
describe("snake case", function () {
    var _loop_1 = function (input, result) {
        it(input + " -> " + result, function () {
            expect(snakeCase(input)).toEqual(result);
        });
    };
    for (var _i = 0, TEST_CASES_1 = TEST_CASES; _i < TEST_CASES_1.length; _i++) {
        var _a = TEST_CASES_1[_i], input = _a[0], result = _a[1];
        _loop_1(input, result);
    }
});
//# sourceMappingURL=index.spec.js.map