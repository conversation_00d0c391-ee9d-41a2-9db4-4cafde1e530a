import _nitro from "./_nitro/preset.mjs";
import _static from "./_static/preset.mjs";
import _alwaysdata from "./alwaysdata/preset.mjs";
import _awsAmplify from "./aws-amplify/preset.mjs";
import _awsLambda from "./aws-lambda/preset.mjs";
import _azure from "./azure/preset.mjs";
import _bun from "./bun/preset.mjs";
import _cleavr from "./cleavr/preset.mjs";
import _cloudflare from "./cloudflare/preset.mjs";
import _deno from "./deno/preset.mjs";
import _digitalocean from "./digitalocean/preset.mjs";
import _edgio from "./edgio/preset.mjs";
import _firebase from "./firebase/preset.mjs";
import _flightcontrol from "./flightcontrol/preset.mjs";
import _genezio from "./genezio/preset.mjs";
import _heroku from "./heroku/preset.mjs";
import _iis from "./iis/preset.mjs";
import _koyeb from "./koyeb/preset.mjs";
import _netlify from "./netlify/preset.mjs";
import _node from "./node/preset.mjs";
import _platformSh from "./platform.sh/preset.mjs";
import _renderCom from "./render.com/preset.mjs";
import _stormkit from "./stormkit/preset.mjs";
import _vercel from "./vercel/preset.mjs";
import _winterjs from "./winterjs/preset.mjs";
import _zeabur from "./zeabur/preset.mjs";
import _zerops from "./zerops/preset.mjs";
export default [
  ..._nitro,
  ..._static,
  ..._alwaysdata,
  ..._awsAmplify,
  ..._awsLambda,
  ..._azure,
  ..._bun,
  ..._cleavr,
  ..._cloudflare,
  ..._deno,
  ..._digitalocean,
  ..._edgio,
  ..._firebase,
  ..._flightcontrol,
  ..._genezio,
  ..._heroku,
  ..._iis,
  ..._koyeb,
  ..._netlify,
  ..._node,
  ..._platformSh,
  ..._renderCom,
  ..._stormkit,
  ..._vercel,
  ..._winterjs,
  ..._zeabur,
  ..._zerops
];
