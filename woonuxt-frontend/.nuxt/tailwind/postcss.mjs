// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 12/08/2025, 09:05:26
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg4 from "./../../woonuxt_base/tailwind.config.ts";
const config = [
{"content":{"files":["/home/<USER>/woonuxt/woonuxt-frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","./components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/plugins/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/composables/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/utils/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/app.config.{js,ts,mjs}"]}},
{},
{"content":{"files":["/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/plugins/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/composables/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/utils/**/*.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/app.config.{js,ts,mjs}","/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/spa-loading-template.html"]}},
cfg4
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;