{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "vue": ["../node_modules/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "vue-router": ["../node_modules/vue-router"], "vue-router/auto-routes": ["../node_modules/vue-router/vue-router-auto-routes"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "#shared/*": ["../shared/*"], "#constants": ["../woonuxt_base/app/constants"], "#constants/*": ["../woonuxt_base/app/constants/*"], "#woo": ["./gql/default"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "#gql": ["./gql"], "#gql/*": ["./gql/*"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables"], "#imports": ["./imports"], "vue-i18n": ["../node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../node_modules/@intlify/utils/dist/h3"], "ufo": ["../node_modules/ufo/dist/index"], "#i18n": ["../node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#internal-i18n-types": ["../node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-i18n/logger": ["./nuxt-i18n-logger"], "#app-manifest": ["./manifest/meta/dev.json"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["./nuxt.d.ts", "../**/*", "../modules/*/runtime/**/*", "../layers/*/app/**/*", "../layers/*/modules/*/runtime/**/*", "../shared/**/*.d.ts", "../modules/*/shared/**/*.d.ts", "../layers/*/shared/**/*.d.ts", "../*.d.ts", "../layers/*/*.d.ts", "./nuxt.node.d.ts", "../modules/*.*", "../nuxt.config.*", "../.config/nuxt.*", "../layers/*/nuxt.config.*", "../layers/*/.config/nuxt.*", "../layers/*/modules/**/*", "../shared/**/*", "../modules/*/shared/**/*", "../layers/*/shared/**/*", "../node_modules/runtime", "../node_modules/dist/runtime"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../woonuxt_base/node_modules", "../node_modules/woonuxt-settings/node_modules", "../node_modules/nuxt-graphql-client/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@nuxt/icon/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@nuxtjs/i18n/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist", "../.data", "../modules/*/runtime/server/**/*", "../layers/*/server/**/*", "../layers/*/modules/*/runtime/server/**/*", "../node_modules/runtime/server", "../node_modules/dist/runtime/server"]}