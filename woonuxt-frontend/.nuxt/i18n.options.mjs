
// @ts-nocheck
import locale_en_45US_46json_a3f04af9 from "#nuxt-i18n/a3f04af9";
import locale_de_45DE_46json_41d5bc60 from "#nuxt-i18n/41d5bc60";
import locale_es_45ES_46json_e3d2652e from "#nuxt-i18n/e3d2652e";
import locale_fr_45FR_46json_a9148361 from "#nuxt-i18n/a9148361";
import locale_it_45IT_46json_24a9d673 from "#nuxt-i18n/24a9d673";
import locale_pt_45BR_46json_e9b001d7 from "#nuxt-i18n/e9b001d7";

export const localeCodes =  [
  "en_US",
  "de_DE",
  "es_ES",
  "fr_FR",
  "it_IT",
  "pt_BR"
]

export const localeLoaders = {
  en_US: [
    {
      key: "locale_en_45US_46json_a3f04af9",
      load: () => Promise.resolve(locale_en_45US_46json_a3f04af9),
      cache: true
    }
  ],
  de_DE: [
    {
      key: "locale_de_45DE_46json_41d5bc60",
      load: () => Promise.resolve(locale_de_45DE_46json_41d5bc60),
      cache: true
    }
  ],
  es_ES: [
    {
      key: "locale_es_45ES_46json_e3d2652e",
      load: () => Promise.resolve(locale_es_45ES_46json_e3d2652e),
      cache: true
    }
  ],
  fr_FR: [
    {
      key: "locale_fr_45FR_46json_a9148361",
      load: () => Promise.resolve(locale_fr_45FR_46json_a9148361),
      cache: true
    }
  ],
  it_IT: [
    {
      key: "locale_it_45IT_46json_24a9d673",
      load: () => Promise.resolve(locale_it_45IT_46json_24a9d673),
      cache: true
    }
  ],
  pt_BR: [
    {
      key: "locale_pt_45BR_46json_e9b001d7",
      load: () => Promise.resolve(locale_pt_45BR_46json_e9b001d7),
      cache: true
    }
  ]
}

export const vueI18nConfigs = []

export const nuxtI18nOptions = {
  restructureDir: false,
  experimental: {
    localeDetector: "",
    switchLocalePathLinkSSR: false,
    autoImportTranslationFunctions: false,
    typedPages: true,
    typedOptionsAndMessages: false,
    generatedLocaleFilePathFormat: "absolute",
    alternateLinkCanonicalQueries: false,
    hmr: true
  },
  bundle: {
    compositionOnly: true,
    runtimeOnly: false,
    fullInstall: true,
    dropMessageCompiler: false,
    optimizeTranslationDirective: true
  },
  compilation: {
    strictMessage: true,
    escapeHtml: false
  },
  customBlocks: {
    defaultSFCLang: "json",
    globalSFCScope: false
  },
  locales: [
    {
      code: "en_US",
      name: "English 🇺🇸",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/en-US.json",
          cache: undefined
        }
      ]
    },
    {
      code: "de_DE",
      name: "Deutsch 🇩🇪",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/de-DE.json",
          cache: undefined
        }
      ]
    },
    {
      code: "es_ES",
      name: "Español 🇪🇸",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/es-ES.json",
          cache: undefined
        }
      ]
    },
    {
      code: "fr_FR",
      name: "Français 🇫🇷",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/fr-FR.json",
          cache: undefined
        }
      ]
    },
    {
      code: "it_IT",
      name: "Italiano 🇮🇹",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/it-IT.json",
          cache: undefined
        }
      ]
    },
    {
      code: "pt_BR",
      name: "Português 🇧🇷",
      files: [
        {
          path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/pt-BR.json",
          cache: undefined
        }
      ]
    }
  ],
  defaultLocale: "en_US",
  defaultDirection: "ltr",
  routesNameSeparator: "___",
  trailingSlash: false,
  defaultLocaleRouteNameSuffix: "default",
  strategy: "no_prefix",
  lazy: false,
  langDir: "locales",
  rootRedirect: undefined,
  detectBrowserLanguage: {
    alwaysRedirect: false,
    cookieCrossOrigin: false,
    cookieDomain: null,
    cookieKey: "i18n_redirected",
    cookieSecure: false,
    fallbackLocale: "",
    redirectOn: "root",
    useCookie: true
  },
  differentDomains: false,
  baseUrl: "",
  customRoutes: "page",
  pages: {},
  skipSettingLocaleOnNavigate: false,
  types: "composition",
  debug: false,
  parallelPlugin: false,
  multiDomainLocales: false,
  i18nModules: []
}

export const normalizedLocales = [
  {
    code: "en_US",
    name: "English 🇺🇸",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/en-US.json",
        cache: undefined
      }
    ]
  },
  {
    code: "de_DE",
    name: "Deutsch 🇩🇪",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/de-DE.json",
        cache: undefined
      }
    ]
  },
  {
    code: "es_ES",
    name: "Español 🇪🇸",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/es-ES.json",
        cache: undefined
      }
    ]
  },
  {
    code: "fr_FR",
    name: "Français 🇫🇷",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/fr-FR.json",
        cache: undefined
      }
    ]
  },
  {
    code: "it_IT",
    name: "Italiano 🇮🇹",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/it-IT.json",
        cache: undefined
      }
    ]
  },
  {
    code: "pt_BR",
    name: "Português 🇧🇷",
    files: [
      {
        path: "/home/<USER>/woonuxt/woonuxt-frontend/woonuxt_base/app/locales/pt-BR.json",
        cache: undefined
      }
    ]
  }
]

export const NUXT_I18N_MODULE_ID = "@nuxtjs/i18n"
export const parallelPlugin = false
export const isSSG = false
export const hasPages = true

export const DEFAULT_COOKIE_KEY = "i18n_redirected"
export const DEFAULT_DYNAMIC_PARAMS_KEY = "nuxtI18nInternal"
export const SWITCH_LOCALE_PATH_LINK_IDENTIFIER = "nuxt-i18n-slp"
/** client **/
if(import.meta.hot) {

function deepEqual(a, b, ignoreKeys = []) {
  // Same reference?
  if (a === b) return true

  // Check if either is null or not an object
  if (a == null || b == null || typeof a !== 'object' || typeof b !== 'object') {
    return false
  }

  // Get top-level keys, excluding ignoreKeys
  const keysA = Object.keys(a).filter(k => !ignoreKeys.includes(k))
  const keysB = Object.keys(b).filter(k => !ignoreKeys.includes(k))

  // Must have the same number of keys (after ignoring)
  if (keysA.length !== keysB.length) {
    return false
  }

  // Check each property
  for (const key of keysA) {
    if (!keysB.includes(key)) {
      return false
    }

    const valA = a[key]
    const valB = b[key]

    // Compare functions stringified
    if (typeof valA === 'function' && typeof valB === 'function') {
      if (valA.toString() !== valB.toString()) {
        return false
      }
    }
    // If nested, do a normal recursive check (no ignoring at deeper levels)
    else if (typeof valA === 'object' && typeof valB === 'object') {
      if (!deepEqual(valA, valB)) {
        return false
      }
    }
    // Compare primitive values
    else if (valA !== valB) {
      return false
    }
  }

  return true
}



async function loadCfg(config) {
  const nuxt = useNuxtApp()
  const { default: resolver } = await config()
  return typeof resolver === 'function' ? await nuxt.runWithContext(() => resolver()) : resolver
}


  import.meta.hot.accept("../woonuxt_base/app/locales/en-US.json", async mod => {
    localeLoaders["en_US"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("en_US")
  })

  import.meta.hot.accept("../woonuxt_base/app/locales/de-DE.json", async mod => {
    localeLoaders["de_DE"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("de_DE")
  })

  import.meta.hot.accept("../woonuxt_base/app/locales/es-ES.json", async mod => {
    localeLoaders["es_ES"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("es_ES")
  })

  import.meta.hot.accept("../woonuxt_base/app/locales/fr-FR.json", async mod => {
    localeLoaders["fr_FR"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("fr_FR")
  })

  import.meta.hot.accept("../woonuxt_base/app/locales/it-IT.json", async mod => {
    localeLoaders["it_IT"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("it_IT")
  })

  import.meta.hot.accept("../woonuxt_base/app/locales/pt-BR.json", async mod => {
    localeLoaders["pt_BR"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("pt_BR")
  })



}
/** client-end **/